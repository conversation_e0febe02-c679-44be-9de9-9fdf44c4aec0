V 0.0.4
T 2025-05-28T01:46:30.981+00:00
ID 1|94088ef6-3b65-11f0-90e3-5ec95e4ae39f
I "sys.platform=darwin"
I "python=3.9.22 | packaged by conda-forge | (main, Apr 14 2025, 23:29:55) \n[Clang 18.1.8 ]"
M a:"tasks.py - web_data_fetch"
SR a|0.005
M c:"Collect tasks"
M d:"setup"
M e:""
P b:c|d|e|e|0
ST b|0.005
M f:"regular"
M g:"\nCollecting task web_data_fetch from: tasks.py\n"
C f|g|0.005
M h:"PASS"
ET h|e|1.088
M j:"web_data_fetch"
M k:"tasks"
M l:"/Users/<USER>/_Mong/rpa-test/tasks.py"
M m:"\n    \uc6f9\uc5d0\uc11c \ub370\uc774\ud130\ub97c \uac00\uc838\uc624\ub294 \uc608\uc81c\n    "
P i:j|k|l|m|37
ST i|1.088
M n:"=========================== "
C f|n|1.088
M o:"Running: "
C f|o|1.088
M p:"task_name"
C p|j|1.089
M q:" ============================\n"
C f|q|1.089
M r:"METHOD"
SE i|r|1.089
M s:"\ud83c\udf10 \uc6f9 \ub370\uc774\ud130 \uac00\uc838\uc624\uae30 \uc2dc\uc791..."
P t:j|e|l|e|41
L I|s|t|41|1.089
M v:"get"
M w:"requests.api"
M x:"/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.9/site-packages/requests/api.py"
P u:v|w|x|e|62
SE u|r|1.089
M y:"url"
M z:"str"
M A:"'https://jsonplaceholder.typicode.com/posts/1'"
EA y|z|A
M B:"params"
M C:"NoneType"
M D:"None"
EA B|C|D
M E:"kwargs"
M F:"dict"
M G:"{}"
EA E|F|G
P H:v|w|x|e|73
M I:"Response"
M J:"<Response [200]>"
R H|I|J|1.557
EE r|h|1.557
P K:j|k|l|e|45
M L:"response"
AS K|L|I|J|1.557
M N:"Response.raise_for_status"
M O:"requests.models"
M P:"/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.9/site-packages/requests/models.py"
P M:N|O|P|e|997
SE M|r|1.557
EE r|h|1.557
M R:"Response.json"
P Q:R|O|P|e|947
SE Q|r|1.557
EA E|F|G
P S:R|O|P|e|974
M T:"{'userId': 1, 'id': 1, 'title': 'sunt aut facere repellat provident occaecati excepturi optio reprehenderit', 'body': 'quia et suscipit\\nsuscipit recusandae consequuntur expedita et cum\\nreprehenderit molestiae ut ut quas totam\\nnostrum rerum est autem sunt rem eveniet architecto'}"
R S|F|T|1.558
EE r|h|1.559
P U:j|k|l|e|48
M V:"data"
AS U|V|F|T|1.559
M W:"\uac00\uc838\uc628 \ub370\uc774\ud130: sunt aut facere repellat provident occaecati excepturi optio reprehenderit"
P X:j|e|l|e|49
L I|W|X|49|1.559
M Y:"\u2705 \uc6f9 \ub370\uc774\ud130 \uac00\uc838\uc624\uae30 \uc644\ub8cc!"
P Z:j|e|l|e|55
L I|Y|Z|55|1.56
EE r|h|1.56
C p|j|1.561
M 0:" status: "
C f|0|1.561
M 1:"PASS\n"
C f|1|1.561
M 2:"================================================================================\n"
C f|2|1.561
ET h|e|1.561
M 4:"Teardown tasks"
M 5:"teardown"
P 3:4|5|e|e|0
ST 3|1.561
M 6:"Process snapshot"
SPS 6|1.562
M 7:"System information:\nMemory: Total: 16.0 G, Available: 705.2 M, Used: 95.7 %\nCPUs: 10"
P 8:e|e|e|e|0
L I|7|8|0|1.671
M 9:"Current Process: python3.9 (pid: 93352, status: running)\nCommand Line: /Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/bin/python3.9 -m robocorp.tasks run tasks.py -t web_data_fetch\nStarted: 10:46:30\nParent pid: 93349\nResident Set Size: 78.4 M\nVirtual Memory Size: 33.1 G"
L I|9|8|0|1.682
M aa:"MainThread|Thread ID: 8603558400 (non daemon)"
STD aa|1.682
M ab:"/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.9/site-packages/robocorp/log/__init__.py"
M ac:"process_snapshot"
M ad:"robo_logger.process_snapshot()"
TBE ab|268|ac|ad
M ae:"logger_instances"
M af:"{<robocorp.log._robo_logger._RoboLogger object at 0x109844bb0>: 1}"
TBV ae|F|af
M ag:"robo_logger"
M ah:"_RoboLogger"
M ai:"<robocorp.log._robo_logger._RoboLogger object at 0x109844bb0>"
TBV ag|ah|ai
M aj:"/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.9/site-packages/robocorp/log/_robo_logger.py"
M ak:"new_func"
M al:"return func(self, *args, **kwargs)"
TBE aj|20|ak|al
M am:"self"
TBV am|ah|ai
M an:"args"
M ao:"tuple"
M ap:"()"
TBV an|ao|ap
TBV E|F|G
M aq:"func"
M ar:"function"
M as:"<function _RoboLogger.process_snapshot at 0x109eb0790>"
TBV aq|ar|as
M at:"return self._robot_output_impl.process_snapshot(hide_vars)"
TBE aj|416|ac|at
TBV am|ah|ai
M au:"hide_vars"
M av:"bool"
M aw:"False"
TBV au|av|aw
M ax:"/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.9/site-packages/robocorp/log/_robo_output_impl.py"
M ay:"self._dump_threads(hide_vars)"
TBE ax|747|ac|ay
TBV au|av|aw
M az:"log"
M aA:"module"
M aB:"<module 'robocorp.log' from '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.9/site-packages/robocorp/log/__init__.py'>"
TBV az|aA|aB
M aC:"entry_id"
M aD:"'ps_0'"
TBV aC|z|aD
M aE:"entry_type"
M aF:"'process_snapshot'"
TBV aE|z|aF
M aG:"psutil"
M aH:"<module 'psutil' from '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.9/site-packages/psutil/__init__.py'>"
TBV aG|aA|aH
M aI:"AccessDenied"
M aJ:"type"
M aK:"<class 'psutil.AccessDenied'>"
TBV aI|aJ|aK
M aL:"NoSuchProcess"
M aM:"<class 'psutil.NoSuchProcess'>"
TBV aL|aJ|aM
M aN:"ZombieProcess"
M aO:"<class 'psutil.ZombieProcess'>"
TBV aN|aJ|aO
M aP:"curr_process"
M aQ:"Process"
M aR:"psutil.Process(pid=93352, name='python3.9', status='running', started='10:46:30')"
TBV aP|aQ|aR
M aS:"log_info"
M aT:"<function _RoboOutputImpl.process_snapshot.<locals>.log_info at 0x10a993790>"
TBV aS|ar|aT
M aU:"memory_info"
M aV:"'Total: 16.0 G, Available: 705.2 M, Used: 95.7 %'"
TBV aU|z|aV
M aW:"child_i"
M aX:"int"
M aY:"0"
TBV aW|aX|aY
M aZ:"child"
TBV aZ|aQ|aR
M a0:"name"
M a1:"'python3.9'"
TBV a0|z|a1
M a2:"status"
M a3:"'running'"
TBV a2|z|a3
M a4:"create_time"
M a5:"'10:46:30'"
TBV a4|z|a5
M a6:"ppid"
M a7:"'93349'"
TBV a6|z|a7
M a8:"cmdline"
M a9:"'/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/bin/python3.9 -m robocorp.tasks run tasks.py -t web_data_fetch'"
TBV a8|z|a9
M ba:"rss"
M bb:"'78.4 M'"
TBV ba|z|bb
M bc:"vms"
M bd:"'33.1 G'"
TBV bc|z|bd
M be:"proc_memory_info"
M bf:"pmem"
M bg:"pmem(rss=82198528, vms=35532394496, pfaults=24208, pageins=902)"
TBV be|bf|bg
M bh:"message"
M bi:"'Current Process: python3.9 (pid: 93352, status: running)\\nCommand Line: /Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/bin/python3.9 -m robocorp.tasks run tasks.py -t web_data_fetch\\nStarted: 10:46:30\\nParent pid: 93349\\nResident Set Size: 78.4 M\\nVirtual Memory Size: 33.1 G'"
TBV bh|z|bi
M bj:"_RoboOutputImpl"
M bk:"<robocorp.log._robo_output_impl._RoboOutputImpl object at 0x109cf59d0>"
TBV am|bj|bk
M bl:"_dump_threads"
M bm:"stack.append((f, f.f_lineno))"
TBE ax|768|bl|bm
TBV am|bj|bk
TBV au|av|aw
M bn:"thread_id"
M bo:"8603558400"
TBV bn|aX|bo
M bp:"frame"
M bq:"<frame at 0x7fadcef28a30, file '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.9/site-packages/robocorp/log/_robo_output_impl.py', line 772, code _dump_threads>"
TBV bp|bp|bq
M br:"thread"
M bs:"_MainThread"
M bt:"<_MainThread(MainThread, started 8603558400)>"
TBV br|bs|bt
M bu:"title"
M bv:"'MainThread|Thread ID: 8603558400 (non daemon)'"
TBV bu|z|bv
M bw:"f"
M bx:"<frame at 0x7fadef13e210, file '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.9/site-packages/robocorp/tasks/_commands.py', line 494, code run>"
TBV bw|bp|bx
M by:"stack"
M bz:"list"
M bA:"[(<frame at 0x10abd9950, file '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.9/site-packages/robocorp/log/__init__.py', line 268, code process_snapshot>, 268), (<frame at 0x7fadefa56f40, file '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.9/site-packages/robocorp/log/_robo_logger.py', line 20, code new_func>, 20), (<frame at 0x10abcf550, file '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.9/site-packages/robocorp/log/_robo_logger.py', line 416, code process_snapshot>, 416), (<frame at 0x7fadefaa6c90, file '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.9/site-packages/robocorp/log/_robo_output_impl.py', line 747, code process_snapshot>, 747), (<frame at 0x7fadcef28a30, file '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.9/site-packages/robocorp/log/_robo_output_impl.py', line 772, code _dump_threads>, 768)]"
TBV by|bz|bA
ETD 1.684
EPS 1.684
ET h|e|1.684
ER h|1.684
