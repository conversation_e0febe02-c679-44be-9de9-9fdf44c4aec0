V 0.0.4
T 2025-05-28T02:29:24.835+00:00
ID 1|922b854c-3b6b-11f0-823c-5ec95e4ae39f
I "sys.platform=darwin"
I "python=3.9.6 (default, Mar 12 2025, 20:22:46) \n[Clang 17.0.0 (clang-1700.0.13.3)]"
M a:"tasks.py - web_data_fetch"
SR a|0.005
M c:"Collect tasks"
M d:"setup"
M e:""
P b:c|d|e|e|0
ST b|0.005
M f:"regular"
M g:"\nCollecting task web_data_fetch from: tasks.py\n"
C f|g|0.005
M h:"stderr"
M i:"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020\n  warnings.warn(\n"
C h|i|0.107
M j:"PASS"
ET j|e|0.153
M l:"web_data_fetch"
M m:"tasks"
M n:"/Users/<USER>/_Mong/rpa-test/tasks.py"
M o:"\n    \uc6f9\uc5d0\uc11c \ub370\uc774\ud130\ub97c \uac00\uc838\uc624\ub294 \uc608\uc81c\n    "
P k:l|m|n|o|37
ST k|0.153
M p:"=========================== "
C f|p|0.153
M q:"Running: "
C f|q|0.153
M r:"task_name"
C r|l|0.153
M s:" ============================\n"
C f|s|0.153
M t:"METHOD"
SE k|t|0.153
M u:"\ud83c\udf10 \uc6f9 \ub370\uc774\ud130 \uac00\uc838\uc624\uae30 \uc2dc\uc791..."
P v:l|e|n|e|41
L I|u|v|41|0.153
M x:"get"
M y:"requests.api"
M z:"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/api.py"
P w:x|y|z|e|62
SE w|t|0.154
M A:"url"
M B:"str"
M C:"'https://jsonplaceholder.typicode.com/posts/1'"
EA A|B|C
M D:"params"
M E:"NoneType"
M F:"None"
EA D|E|F
M G:"kwargs"
M H:"dict"
M I:"{}"
EA G|H|I
P J:x|y|z|e|73
M K:"Response"
M L:"<Response [200]>"
R J|K|L|0.742
EE t|j|0.742
P M:l|m|n|e|45
M N:"response"
AS M|N|K|L|0.742
M P:"Response.raise_for_status"
M Q:"requests.models"
M R:"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/models.py"
P O:P|Q|R|e|997
SE O|t|0.742
EE t|j|0.742
M T:"Response.json"
P S:T|Q|R|e|947
SE S|t|0.742
EA G|H|I
P U:T|Q|R|e|974
M V:"{'userId': 1, 'id': 1, 'title': 'sunt aut facere repellat provident occaecati excepturi optio reprehenderit', 'body': 'quia et suscipit\\nsuscipit recusandae consequuntur expedita et cum\\nreprehenderit molestiae ut ut quas totam\\nnostrum rerum est autem sunt rem eveniet architecto'}"
R U|H|V|0.742
EE t|j|0.742
P W:l|m|n|e|48
M X:"data"
AS W|X|H|V|0.743
M Y:"\uac00\uc838\uc628 \ub370\uc774\ud130: sunt aut facere repellat provident occaecati excepturi optio reprehenderit"
P Z:l|e|n|e|49
L I|Y|Z|49|0.743
M 0:"\u2705 \uc6f9 \ub370\uc774\ud130 \uac00\uc838\uc624\uae30 \uc644\ub8cc!"
P 1:l|e|n|e|55
L I|0|1|55|0.744
EE t|j|0.744
C r|l|0.745
M 2:" status: "
C f|2|0.745
M 3:"PASS\n"
C f|3|0.745
M 4:"================================================================================\n"
C f|4|0.745
ET j|e|0.745
M 6:"Teardown tasks"
M 7:"teardown"
P 5:6|7|e|e|0
ST 5|0.745
M 8:"Process snapshot"
SPS 8|0.745
M 9:"System information:\nMemory: Total: 16.0 G, Available: 3.0 G, Used: 81.2 %\nCPUs: 10"
P aa:e|e|e|e|0
L I|9|aa|0|0.769
M ab:"Current Process: Python (pid: 10903, status: running)\nCommand Line: /Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Resources/Python.app/Contents/MacOS/Python -m robocorp.tasks run tasks.py -t web_data_fetch\nStarted: 11:29:24\nParent pid: 87559\nResident Set Size: 33.8 M\nVirtual Memory Size: 391.8 G"
L I|ab|aa|0|0.787
M ac:"MainThread|Thread ID: 8293916416 (non daemon)"
STD ac|0.787
M ad:"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/robocorp/log/__init__.py"
M ae:"process_snapshot"
M af:"robo_logger.process_snapshot()"
TBE ad|268|ae|af
M ag:"logger_instances"
M ah:"{<robocorp.log._robo_logger._RoboLogger object at 0x105661280>: 1}"
TBV ag|H|ah
M ai:"robo_logger"
M aj:"_RoboLogger"
M ak:"<robocorp.log._robo_logger._RoboLogger object at 0x105661280>"
TBV ai|aj|ak
M al:"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/robocorp/log/_robo_logger.py"
M am:"new_func"
M an:"return func(self, *args, **kwargs)"
TBE al|20|am|an
M ao:"self"
TBV ao|aj|ak
M ap:"args"
M aq:"tuple"
M ar:"()"
TBV ap|aq|ar
TBV G|H|I
M as:"func"
M at:"function"
M au:"<function _RoboLogger.process_snapshot at 0x10566af70>"
TBV as|at|au
M av:"return self._robot_output_impl.process_snapshot(hide_vars)"
TBE al|416|ae|av
TBV ao|aj|ak
M aw:"hide_vars"
M ax:"bool"
M ay:"False"
TBV aw|ax|ay
M az:"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/robocorp/log/_robo_output_impl.py"
M aA:"self._dump_threads(hide_vars)"
TBE az|747|ae|aA
TBV aw|ax|ay
M aB:"log"
M aC:"module"
M aD:"<module 'robocorp.log' from '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/robocorp/log/__init__.py'>"
TBV aB|aC|aD
M aE:"entry_id"
M aF:"'ps_0'"
TBV aE|B|aF
M aG:"entry_type"
M aH:"'process_snapshot'"
TBV aG|B|aH
M aI:"psutil"
M aJ:"<module 'psutil' from '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/psutil/__init__.py'>"
TBV aI|aC|aJ
M aK:"AccessDenied"
M aL:"type"
M aM:"<class 'psutil.AccessDenied'>"
TBV aK|aL|aM
M aN:"NoSuchProcess"
M aO:"<class 'psutil.NoSuchProcess'>"
TBV aN|aL|aO
M aP:"ZombieProcess"
M aQ:"<class 'psutil.ZombieProcess'>"
TBV aP|aL|aQ
M aR:"curr_process"
M aS:"Process"
M aT:"psutil.Process(pid=10903, name='Python', status='running', started='11:29:24')"
TBV aR|aS|aT
M aU:"log_info"
M aV:"<function _RoboOutputImpl.process_snapshot.<locals>.log_info at 0x105f13e50>"
TBV aU|at|aV
M aW:"memory_info"
M aX:"'Total: 16.0 G, Available: 3.0 G, Used: 81.2 %'"
TBV aW|B|aX
M aY:"child_i"
M aZ:"int"
M a0:"0"
TBV aY|aZ|a0
M a1:"child"
TBV a1|aS|aT
M a2:"name"
M a3:"'Python'"
TBV a2|B|a3
M a4:"status"
M a5:"'running'"
TBV a4|B|a5
M a6:"create_time"
M a7:"'11:29:24'"
TBV a6|B|a7
M a8:"ppid"
M a9:"'87559'"
TBV a8|B|a9
M ba:"cmdline"
M bb:"'/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Resources/Python.app/Contents/MacOS/Python -m robocorp.tasks run tasks.py -t web_data_fetch'"
TBV ba|B|bb
M bc:"rss"
M bd:"'33.8 M'"
TBV bc|B|bd
M be:"vms"
M bf:"'391.8 G'"
TBV be|B|bf
M bg:"proc_memory_info"
M bh:"pmem"
M bi:"pmem(rss=35487744, vms=420716724224, pfaults=4050, pageins=543)"
TBV bg|bh|bi
M bj:"message"
M bk:"'Current Process: Python (pid: 10903, status: running)\\nCommand Line: /Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/Resources/Python.app/Contents/MacOS/Python -m robocorp.tasks run tasks.py -t web_data_fetch\\nStarted: 11:29:24\\nParent pid: 87559\\nResident Set Size: 33.8 M\\nVirtual Memory Size: 391.8 G'"
TBV bj|B|bk
M bl:"_RoboOutputImpl"
M bm:"<robocorp.log._robo_output_impl._RoboOutputImpl object at 0x1049f4cd0>"
TBV ao|bl|bm
M bn:"_dump_threads"
M bo:"stack.append((f, f.f_lineno))"
TBE az|768|bn|bo
TBV ao|bl|bm
TBV aw|ax|ay
M bp:"thread_id"
M bq:"8293916416"
TBV bp|aZ|bq
M br:"frame"
M bs:"<frame at 0x144931810, file '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/robocorp/log/_robo_output_impl.py', line 772, code _dump_threads>"
TBV br|br|bs
M bt:"thread"
M bu:"_MainThread"
M bv:"<_MainThread(MainThread, started 8293916416)>"
TBV bt|bu|bv
M bw:"title"
M bx:"'MainThread|Thread ID: 8293916416 (non daemon)'"
TBV bw|B|bx
M by:"f"
M bz:"<frame at 0x1458a5210, file '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/robocorp/tasks/_commands.py', line 494, code run>"
TBV by|br|bz
M bA:"stack"
M bB:"list"
M bC:"[(<frame at 0x105f7c040, file '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/robocorp/log/__init__.py', line 268, code process_snapshot>, 268), (<frame at 0x143e5b720, file '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/robocorp/log/_robo_logger.py', line 20, code new_func>, 20), (<frame at 0x105f793a0, file '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/robocorp/log/_robo_logger.py', line 416, code process_snapshot>, 416), (<frame at 0x113f04c80, file '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/robocorp/log/_robo_output_impl.py', line 747, code process_snapshot>, 747), (<frame at 0x144931810, file '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/robocorp/log/_robo_output_impl.py', line 772, code _dump_threads>, 768)]"
TBV bA|bB|bC
ETD 0.79
EPS 0.79
ET j|e|0.79
ER j|0.79
