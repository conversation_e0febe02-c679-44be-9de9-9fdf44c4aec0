{"when":1748396688,"controller":"rcc.user","event":"start task","detail":"name=Hello Robocorp from=robot.yaml","comment":"at task environment setup"}
{"when":1748396688,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 00/15 v18.5.0 0.053s Context: \"HoMongJun\" \<EMAIL>\u003e [darwin_amd64/ProductName: macOS; ProductVersion: 15.5; BuildVersion: 24F74]."}
{"when":1748396688,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 01/15 v18.5.0 0.004s Fresh [private mode] holotree environment 98a9f8cc-844e-95a0-cd3a-2d033d43c941. (parent/pid: 87559/92443)"}
{"when":1748396688,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 02/15 v18.5.0 0.009s Holotree blueprint is \"e5b52991b610e094\" [darwin_amd64 with 9 workers on 10 CPUs from \"conda.yaml\"]."}
{"when":1748396688,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 03/15 v18.5.0 0.002s Fill hololib from RCC_REMOTE_ORIGIN skipped. RCC_REMOTE_ORIGIN was not defined."}
{"when":1748396688,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 04/15 v18.5.0 0.002s Cleanup holotree stage for fresh install."}
{"when":1748396688,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 05/15 v18.5.0 0.001s Build environment into holotree stage \"/Users/<USER>/.robocorp/holotree/h51f0201_0cc3ca830dec7ft\"."}
{"when":1748396688,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 06/15 v18.5.0 0.001s Restore partial environment into holotree stage \"/Users/<USER>/.robocorp/holotree/h51f0201_0cc3ca830dec7ft\"."}
{"when":1748396688,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 07/15 v18.5.0 0.116s Running micromamba phase. (micromamba v1.5.8) [layer: 277962fe7fa06395]"}
{"when":1748396712,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 08/15 v18.5.0 23.608s Running pip install phase. (pip v24.2) [layer: e5b52991b610e094]"}
{"when":1748396716,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 09/15 v18.5.0 4.442s Post install scripts phase skipped -- no scripts."}
{"when":1748396716,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 10/15 v18.5.0 0.010s Activate environment started phase."}
{"when":1748396718,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 11/15 v18.5.0 1.330s Pip check skipped."}
{"when":1748396718,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 12/15 v18.5.0 0.005s Update installation plan."}
{"when":1748396718,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 13/15 v18.5.0 0.005s Record holotree stage to hololib [with 9 workers on 10 CPUs]."}
{"when":1748396718,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 14/15 v18.5.0 0.481s Restore space from library [with 9 workers on 10 CPUs; with compression: true]."}
{"when":1748396720,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 15/15 v18.5.0 1.916s Fresh holotree done [with 9 workers on 10 CPUs]."}
{"when":1748396720,"controller":"rcc.user","event":"start","detail":"robot","comment":"started"}
{"when":1748396720,"controller":"rcc.user","event":"run","detail":"robot","comment":"task run"}
{"when":1748396720,"controller":"rcc.user","event":"processes","detail":"updated","comment":"count from 0 to 1 ... map[92724:rcc]"}
{"when":1748396721,"controller":"rcc.user","event":"processes","detail":"updated","comment":"count from 1 to 1 ... map[92724:python3.9]"}
{"when":1748396727,"controller":"rcc.user","event":"robot exit","detail":"From rcc \"v18.5.0\" (controller: \"user\") point of view, \"actual main robot run\" was FAILURE, reason: exit status 1","comment":"rcc point of view"}
{"when":1748396727,"controller":"rcc.user","event":"processes","detail":"final","comment":"count: 0"}
{"when":1748396728,"controller":"rcc.user","event":"stop","detail":"robot","comment":"done"}
{"when":1748396755,"controller":"rcc.user","event":"start task","detail":"name=Hello Robocorp from=robot.yaml","comment":"at task environment setup"}
{"when":1748396755,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 00/15 v18.5.0 0.063s Context: \"HoMongJun\" \<EMAIL>\u003e [darwin_amd64/ProductName: macOS; ProductVersion: 15.5; BuildVersion: 24F74]."}
{"when":1748396755,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 01/15 v18.5.0 0.006s Fresh [private mode] holotree environment 98a9f8cc-844e-95a0-cd3a-2d033d43c941. (parent/pid: 87559/93060)"}
{"when":1748396755,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 02/15 v18.5.0 0.004s Holotree blueprint is \"becc153d26099272\" [darwin_amd64 with 9 workers on 10 CPUs from \"conda.yaml\"]."}
{"when":1748396755,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 03/15 v18.5.0 0.002s Fill hololib from RCC_REMOTE_ORIGIN skipped. RCC_REMOTE_ORIGIN was not defined."}
{"when":1748396755,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 04/15 v18.5.0 0.003s Cleanup holotree stage for fresh install."}
{"when":1748396755,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 05/15 v18.5.0 0.003s Build environment into holotree stage \"/Users/<USER>/.robocorp/holotree/h51f0201_0cc3ca830dec7ft\"."}
{"when":1748396755,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 06/15 v18.5.0 0.001s Restore partial environment into holotree stage \"/Users/<USER>/.robocorp/holotree/h51f0201_0cc3ca830dec7ft\"."}
{"when":1748396756,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 07/15 v18.5.0 1.208s Skipping micromamba phase, layer exists."}
{"when":1748396765,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 08/15 v18.5.0 9.654s Running pip install phase. (pip v24.2) [layer: becc153d26099272]"}
{"when":1748396771,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 09/15 v18.5.0 5.064s Post install scripts phase skipped -- no scripts."}
{"when":1748396771,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 10/15 v18.5.0 0.017s Activate environment started phase."}
{"when":1748396772,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 11/15 v18.5.0 1.093s Pip check skipped."}
{"when":1748396772,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 12/15 v18.5.0 0.006s Update installation plan."}
{"when":1748396772,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 13/15 v18.5.0 0.009s Record holotree stage to hololib [with 9 workers on 10 CPUs]."}
{"when":1748396772,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 14/15 v18.5.0 0.825s Restore space from library [with 9 workers on 10 CPUs; with compression: true]."}
{"when":1748396774,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 15/15 v18.5.0 1.700s Fresh holotree done [with 9 workers on 10 CPUs]."}
{"when":1748396774,"controller":"rcc.user","event":"start","detail":"robot","comment":"started"}
{"when":1748396774,"controller":"rcc.user","event":"run","detail":"robot","comment":"task run"}
{"when":1748396775,"controller":"rcc.user","event":"processes","detail":"updated","comment":"count from 0 to 1 ... map[93236:python3.9]"}
{"when":1748396779,"controller":"rcc.user","event":"robot exit","detail":"From rcc \"v18.5.0\" (controller: \"user\") point of view, \"actual main robot run\" was SUCCESS.","comment":"rcc point of view"}
{"when":1748396779,"controller":"rcc.user","event":"processes","detail":"final","comment":"count: 0"}
{"when":1748396780,"controller":"rcc.user","event":"stop","detail":"robot","comment":"done"}
{"when":1748396790,"controller":"rcc.user","event":"start task","detail":"name=Web Data Fetch from=robot.yaml","comment":"at task environment setup"}
{"when":1748396790,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 00/15 v18.5.0 0.054s Context: \"HoMongJun\" \<EMAIL>\u003e [darwin_amd64/ProductName: macOS; ProductVersion: 15.5; BuildVersion: 24F74]."}
{"when":1748396790,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 01/15 v18.5.0 0.004s Fresh [private mode] holotree environment 98a9f8cc-844e-95a0-cd3a-2d033d43c941. (parent/pid: 87559/93349)"}
{"when":1748396790,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 02/15 v18.5.0 0.002s Holotree blueprint is \"becc153d26099272\" [darwin_amd64 with 9 workers on 10 CPUs from \"conda.yaml\"]."}
{"when":1748396790,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 14/15 v18.5.0 0.085s Restore space from library [with 9 workers on 10 CPUs; with compression: true]."}
{"when":1748396790,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 15/15 v18.5.0 0.214s Fresh holotree done [with 9 workers on 10 CPUs]."}
{"when":1748396790,"controller":"rcc.user","event":"start","detail":"robot","comment":"started"}
{"when":1748396790,"controller":"rcc.user","event":"run","detail":"robot","comment":"task run"}
{"when":1748396791,"controller":"rcc.user","event":"processes","detail":"updated","comment":"count from 0 to 1 ... map[93352:python3.9]"}
{"when":1748396792,"controller":"rcc.user","event":"robot exit","detail":"From rcc \"v18.5.0\" (controller: \"user\") point of view, \"actual main robot run\" was SUCCESS.","comment":"rcc point of view"}
{"when":1748396792,"controller":"rcc.user","event":"processes","detail":"final","comment":"count: 0"}
{"when":1748396793,"controller":"rcc.user","event":"stop","detail":"robot","comment":"done"}
