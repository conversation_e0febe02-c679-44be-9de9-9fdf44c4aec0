# For more details on the format and content:
# https://github.com/robocorp/rcc/blob/master/docs/recipes.md#what-is-in-robotyaml

tasks:
  Run Task:
    shell: python -m robocorp.tasks run tasks.py

environmentConfigs:
  - environment_windows_amd64_freeze.yaml
  - environment_linux_amd64_freeze.yaml
  - environment_darwin_amd64_freeze.yaml
  - conda.yaml

artifactsDir: output

PATH:
  - .
PYTHONPATH:
  - .
ignoreFiles:
  - .gitignore
