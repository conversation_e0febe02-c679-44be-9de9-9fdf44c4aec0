channels:
- conda-forge
dependencies:
- bzip2=1.0.8
- ca-certificates=2025.4.26
- libexpat=2.7.0
- libffi=3.4.6
- liblzma=5.8.1
- libsqlite=3.49.2
- libzlib=1.3.1
- ncurses=6.5
- openssl=3.5.0
- pip=24.3.1
- python=3.12.8
- readline=8.2
- robocorp-truststore=0.8.0
- setuptools=80.8.0
- tk=8.6.13
- tzdata=2025b
- wheel=0.45.1
- pip:
  - attrs==25.3.0
  - beautifulsoup4==4.13.4
  - cached-property==2.0.1
  - certifi==2025.4.26
  - cffi==1.17.1
  - chardet==3.0.4
  - charset-normalizer==3.4.2
  - click==8.2.1
  - cryptography==44.0.3
  - dataclasses-json==0.6.7
  - defusedxml==0.7.1
  - dnspython==2.7.0
  - docstring_parser_fork==0.0.5
  - docutils==0.21.2
  - et_xmlfile==2.0.0
  - exchangelib==5.5.1
  - fonttools==4.58.0
  - fpdf2==2.7.5
  - furl==2.1.4
  - graphviz==0.13.2
  - greenlet==3.2.2
  - h11==0.16.0
  - holidays==0.45
  - htmldocx==0.0.6
  - idna==3.10
  - importlib_metadata==8.7.0
  - isodate==0.7.2
  - java-access-bridge-wrapper==1.2.0
  - jsonpath-ng==1.7.0
  - jsonschema==4.24.0
  - jsonschema-specifications==2025.4.1
  - lxml==5.4.0
  - marshmallow==3.26.1
  - more-itertools==10.7.0
  - mss==6.1.0
  - mypy_extensions==1.1.0
  - netsuitesdk==1.24.0
  - notifiers==1.3.6
  - O365==2.0.26
  - oauthlib==3.2.2
  - openpyxl==3.1.5
  - orderedmultidict==1.0.1
  - outcome==1.3.0.post0
  - packaging==24.2
  - pdfminer.six==20221105
  - pendulum==3.1.0
  - pillow==10.4.0
  - platformdirs==4.3.8
  - playwright==1.52.0
  - ply==3.11
  - psutil==5.9.8
  - pycparser==2.22
  - pyee==13.0.0
  - Pygments==2.19.1
  - PyJWT==2.10.1
  - pynput-robocorp-fork==5.0.0
  - pyobjc-core==9.2
  - pyobjc-framework-ApplicationServices==9.2
  - pyobjc-framework-Cocoa==9.2
  - pyobjc-framework-Quartz==9.2
  - pyotp==2.9.0
  - pypdf==3.17.4
  - pyperclip==1.9.0
  - PySocks==1.7.1
  - pyspnego==0.11.2
  - python-dateutil==2.9.0.post0
  - python-docx==0.8.11
  - python-dotenv==1.1.0
  - pytz==2025.2
  - pytz-deprecation-shim==0.1.0.post0
  - PyYAML==6.0.2
  - referencing==0.36.2
  - requests==2.32.3
  - requests-file==2.1.0
  - requests-oauthlib==1.3.1
  - requests-toolbelt==1.0.0
  - requests_ntlm==1.3.0
  - robocorp==2.1.3
  - robocorp-browser==2.3.4
  - robocorp-log==2.9.6
  - robocorp-storage==1.0.5
  - robocorp-tasks==3.1.2
  - robocorp-vault==1.3.8
  - robocorp-workitems==1.4.7
  - robotframework==7.2.2
  - robotframework-pythonlibcore==4.4.1
  - robotframework-requests==0.9.7
  - robotframework-seleniumlibrary==6.7.1
  - robotframework-seleniumtestability==2.1.0
  - rpaframework==30.0.1
  - rpaframework-core==12.0.1
  - rpaframework-pdf==8.0.0
  - rpds-py==0.25.1
  - selenium==4.15.2
  - simple-salesforce==1.12.6
  - six==1.17.0
  - smartsheet-python-sdk==3.0.2
  - sniffio==1.3.1
  - sortedcontainers==2.4.0
  - soupsieve==2.7
  - stringcase==1.2.0
  - tenacity==8.5.0
  - trio==0.30.0
  - trio-websocket==0.12.2
  - tweepy==3.10.0
  - typing-inspect==0.9.0
  - typing_extensions==4.13.2
  - tzlocal==4.3.1
  - urllib3==2.4.0
  - validators==0.34.0
  - webdriver-manager==4.0.2
  - wrapt==1.17.2
  - wsproto==1.2.0
  - xlrd==2.0.1
  - xlutils==2.0.0
  - xlwt==1.3.0
  - zeep==4.3.1
  - zipp==3.22.0
