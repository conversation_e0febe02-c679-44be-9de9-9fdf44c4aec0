{"when":1748396525,"controller":"rcc.user","event":"start task","detail":"name= from=robot.yaml","comment":"at task environment setup"}
{"when":1748396525,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 00/15 v18.5.0 0.089s Context: \"HoMongJun\" \<EMAIL>\u003e [darwin_amd64/ProductName: macOS; ProductVersion: 15.5; BuildVersion: 24F74]."}
{"when":1748396525,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 01/15 v18.5.0 0.013s Fresh [private mode] holotree environment 98a9f8cc-844e-95a0-cd3a-2d033d43c941. (parent/pid: 87559/91243)"}
{"when":1748396525,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 02/15 v18.5.0 0.004s Holotree blueprint is \"616c2d532b08ea58\" [darwin_amd64 with 9 workers on 10 CPUs from \"conda.yaml\"]."}
{"when":1748396525,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 03/15 v18.5.0 0.003s Fill hololib from RCC_REMOTE_ORIGIN skipped. RCC_REMOTE_ORIGIN was not defined."}
{"when":1748396525,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 04/15 v18.5.0 0.003s Cleanup holotree stage for fresh install."}
{"when":1748396525,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 05/15 v18.5.0 0.001s Build environment into holotree stage \"/Users/<USER>/.robocorp/holotree/h51f0201_0cc3ca830dec7ft\"."}
{"when":1748396525,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 06/15 v18.5.0 0.003s Restore partial environment into holotree stage \"/Users/<USER>/.robocorp/holotree/h51f0201_0cc3ca830dec7ft\"."}
{"when":1748396527,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 07/15 v18.5.0 1.590s Running micromamba phase. (micromamba v1.5.8) [layer: 643de4e95317a9c4]"}
{"when":1748396571,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 08/15 v18.5.0 44.501s Running pip install phase. (pip v24.3.1) [layer: 616c2d532b08ea58]"}
{"when":1748396612,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 09/15 v18.5.0 40.474s Post install scripts phase skipped -- no scripts."}
{"when":1748396612,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 10/15 v18.5.0 0.003s Activate environment started phase."}
{"when":1748396614,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 11/15 v18.5.0 1.575s Pip check skipped."}
{"when":1748396614,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 12/15 v18.5.0 0.003s Update installation plan."}
{"when":1748396614,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 13/15 v18.5.0 0.002s Record holotree stage to hololib [with 9 workers on 10 CPUs]."}
{"when":1748396618,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 14/15 v18.5.0 3.992s Restore space from library [with 9 workers on 10 CPUs; with compression: true]."}
{"when":1748396621,"controller":"rcc.user","event":"environment","detail":"build","comment":"Progress: 15/15 v18.5.0 3.621s Fresh holotree done [with 9 workers on 10 CPUs]."}
{"when":1748396621,"controller":"rcc.user","event":"start","detail":"robot","comment":"started"}
{"when":1748396621,"controller":"rcc.user","event":"run","detail":"robot","comment":"task run"}
{"when":1748396622,"controller":"rcc.user","event":"processes","detail":"updated","comment":"count from 0 to 1 ... map[91948:python3.12]"}
{"when":1748396632,"controller":"rcc.user","event":"robot exit","detail":"From rcc \"v18.5.0\" (controller: \"user\") point of view, \"actual main robot run\" was SUCCESS.","comment":"rcc point of view"}
{"when":1748396632,"controller":"rcc.user","event":"processes","detail":"final","comment":"count: 0"}
{"when":1748396634,"controller":"rcc.user","event":"stop","detail":"robot","comment":"done"}
