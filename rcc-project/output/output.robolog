V 0.0.4
T 2025-05-28T01:43:52.358+00:00
ID 1|357cba1a-3b65-11f0-b7ad-5ec95e4ae39f
I "sys.platform=darwin"
I "python=3.12.8 | packaged by conda-forge | (main, Dec  5 2024, 14:25:12) [Clang 18.1.8 ]"
M a:"tasks.py"
SR a|0.002
M c:"Collect tasks"
M d:"setup"
M e:""
P b:c|d|e|e|0
ST b|0.002
M f:"regular"
M g:"\nCollecting tasks from: tasks.py\n"
C f|g|0.003
M h:"PASS"
ET h|e|0.003
M j:"minimal_task"
M k:"tasks"
M l:"/Users/<USER>/_Mong/rpa-test/rcc-project/tasks.py"
M m:null
P i:j|k|l|m|4
ST i|0.003
M n:"============================ "
C f|n|0.003
M o:"Running: "
C f|o|0.004
M p:"task_name"
C p|j|0.004
M q:" =============================\n"
C f|q|0.004
M r:"METHOD"
SE i|r|0.004
P s:j|k|l|e|5
M t:"message"
M u:"str"
M v:"'Hello'"
AS s|t|u|v|0.004
P w:j|k|l|e|6
M x:"'Hello World!'"
AS w|t|u|x|0.004
EE r|h|0.004
C p|j|0.004
M y:" status: "
C f|y|0.004
M z:"PASS\n"
C f|z|0.004
M A:"================================================================================\n"
C f|A|0.004
ET h|e|0.004
M C:"Teardown tasks"
M D:"teardown"
P B:C|D|e|e|0
ST B|0.004
M E:"Process snapshot"
SPS E|0.005
M F:"System information:\nMemory: Total: 16.0 G, Available: 772.0 M, Used: 95.3 %\nCPUs: 10"
P G:e|e|e|e|0
L I|F|G|0|0.592
M H:"Current Process: python3.12 (pid: 91948, status: running)\nCommand Line: /Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/bin/python3.12 -m robocorp.tasks run tasks.py\nStarted: 10:43:41\nParent pid: 91243\nResident Set Size: 51.5 M\nVirtual Memory Size: 33.0 G"
L I|H|G|0|0.604
M I:"MainThread|Thread ID: 8680239616 (non daemon)"
STD I|0.604
M J:"/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.12/site-packages/robocorp/log/__init__.py"
M K:"process_snapshot"
M L:"robo_logger.process_snapshot()"
TBE J|270|K|L
M M:"logger_instances"
M N:"dict"
M O:"{<robocorp.log._robo_logger._RoboLogger object at 0x10ebe0650>: 1}"
TBV M|N|O
M P:"robo_logger"
M Q:"_RoboLogger"
M R:"<robocorp.log._robo_logger._RoboLogger object at 0x10ebe0650>"
TBV P|Q|R
M S:"/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.12/site-packages/robocorp/log/_robo_logger.py"
M T:"new_func"
M U:"return func(self, *args, **kwargs)"
TBE S|20|T|U
M V:"self"
TBV V|Q|R
M W:"args"
M X:"tuple"
M Y:"()"
TBV W|X|Y
M Z:"kwargs"
M 0:"{}"
TBV Z|N|0
M 1:"func"
M 2:"function"
M 3:"<function _RoboLogger.process_snapshot at 0x10ee316c0>"
TBV 1|2|3
M 4:"return self._robot_output_impl.process_snapshot(hide_vars)"
TBE S|416|K|4
TBV V|Q|R
M 5:"hide_vars"
M 6:"bool"
M 7:"False"
TBV 5|6|7
M 8:"/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.12/site-packages/robocorp/log/_robo_output_impl.py"
M 9:"self._dump_threads(hide_vars)"
TBE 8|748|K|9
M aa:"_RoboOutputImpl"
M ab:"<robocorp.log._robo_output_impl._RoboOutputImpl object at 0x10e18ec90>"
TBV V|aa|ab
TBV 5|6|7
M ac:"log"
M ad:"module"
M ae:"<module 'robocorp.log' from '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.12/site-packages/robocorp/log/__init__.py'>"
TBV ac|ad|ae
M af:"entry_id"
M ag:"'ps_0'"
TBV af|u|ag
M ah:"entry_type"
M ai:"'process_snapshot'"
TBV ah|u|ai
M aj:"psutil"
M ak:"<module 'psutil' from '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.12/site-packages/psutil/__init__.py'>"
TBV aj|ad|ak
M al:"AccessDenied"
M am:"type"
M an:"<class 'psutil.AccessDenied'>"
TBV al|am|an
M ao:"NoSuchProcess"
M ap:"<class 'psutil.NoSuchProcess'>"
TBV ao|am|ap
M aq:"ZombieProcess"
M ar:"<class 'psutil.ZombieProcess'>"
TBV aq|am|ar
M as:"curr_process"
M at:"Process"
M au:"psutil.Process(pid=91948, name='python3.12', status='running', started='10:43:41')"
TBV as|at|au
M av:"log_info"
M aw:"<function _RoboOutputImpl.process_snapshot.<locals>.log_info at 0x10ee656c0>"
TBV av|2|aw
M ax:"memory_info"
M ay:"'Total: 16.0 G, Available: 772.0 M, Used: 95.3 %'"
TBV ax|u|ay
M az:"child_i"
M aA:"int"
M aB:"0"
TBV az|aA|aB
M aC:"child"
TBV aC|at|au
M aD:"name"
M aE:"'python3.12'"
TBV aD|u|aE
M aF:"status"
M aG:"'running'"
TBV aF|u|aG
M aH:"create_time"
M aI:"'10:43:41'"
TBV aH|u|aI
M aJ:"ppid"
M aK:"'91243'"
TBV aJ|u|aK
M aL:"cmdline"
M aM:"'/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/bin/python3.12 -m robocorp.tasks run tasks.py'"
TBV aL|u|aM
M aN:"rss"
M aO:"'51.5 M'"
TBV aN|u|aO
M aP:"vms"
M aQ:"'33.0 G'"
TBV aP|u|aQ
M aR:"proc_memory_info"
M aS:"pmem"
M aT:"pmem(rss=54034432, vms=35426267136, pfaults=16132, pageins=22)"
TBV aR|aS|aT
M aU:"'Current Process: python3.12 (pid: 91948, status: running)\\nCommand Line: /Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/bin/python3.12 -m robocorp.tasks run tasks.py\\nStarted: 10:43:41\\nParent pid: 91243\\nResident Set Size: 51.5 M\\nVirtual Memory Size: 33.0 G'"
TBV t|u|aU
M aV:"_dump_threads"
M aW:"stack.append((f, f.f_lineno))"
TBE 8|769|aV|aW
TBV V|aa|ab
TBV 5|6|7
M aX:"thread_id"
M aY:"8680239616"
TBV aX|aA|aY
M aZ:"frame"
M a0:"<frame at 0x10eeae460, file '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.12/site-packages/robocorp/log/_robo_output_impl.py', line 773, code _dump_threads>"
TBV aZ|aZ|a0
M a1:"thread"
M a2:"_MainThread"
M a3:"<_MainThread(MainThread, started 8680239616)>"
TBV a1|a2|a3
M a4:"title"
M a5:"'MainThread|Thread ID: 8680239616 (non daemon)'"
TBV a4|u|a5
M a6:"f"
M a7:"<frame at 0x7fa69605e160, file '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.12/site-packages/robocorp/tasks/_commands.py', line 494, code run>"
TBV a6|aZ|a7
M a8:"stack"
M a9:"list"
M ba:"[(<frame at 0x10eff0110, file '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.12/site-packages/robocorp/log/__init__.py', line 270, code process_snapshot>, 270), (<frame at 0x10eeb99a0, file '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.12/site-packages/robocorp/log/_robo_logger.py', line 20, code new_func>, 20), (<frame at 0x10ee81f00, file '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.12/site-packages/robocorp/log/_robo_logger.py', line 416, code process_snapshot>, 416), (<frame at 0x10ec763e0, file '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.12/site-packages/robocorp/log/_robo_output_impl.py', line 748, code process_snapshot>, 748), (<frame at 0x10eeae460, file '/Users/<USER>/.robocorp/holotree/51f0201_5a1fac3_9fcd2534/lib/python3.12/site-packages/robocorp/log/_robo_output_impl.py', line 773, code _dump_threads>, 769)]"
TBV a8|a9|ba
ETD 0.605
EPS 0.605
ET h|e|0.605
ER h|0.605
