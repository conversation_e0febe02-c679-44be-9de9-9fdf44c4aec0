# For more details on the format and content:
# https://github.com/robocorp/rcc/blob/master/docs/recipes.md#what-is-in-condayaml
# Tip: Adding a link to the release notes of the packages helps maintenance and security.

channels:
  - conda-forge

dependencies:
  - python=3.12.8                 # https://pyreadiness.org/3.10
  - pip=24.3.1                    # https://pip.pypa.io/en/stable/news
  - robocorp-truststore=0.8.0     # https://pypi.org/project/robocorp-truststore/
  - pip:
    - rpaframework==30.0.1        # https://rpaframework.org/releasenotes.html
    - robocorp==2.1.3             # https://pypi.org/project/robocorp
    - robocorp-browser==2.3.4     # https://pypi.org/project/robocorp-browser