"""
첫 번째 Robocorp 자동화 예제
간단한 웹 스크래핑과 데이터 처리를 수행합니다.
"""

from robocorp.tasks import task
from robocorp import log
from robocorp import browser
import requests
import json


@task
def hello_robocorp():
    """
    Robocorp 시작하기 - 기본 작업
    """
    log.info("🤖 Robocorp 자동화 시작!")

    # 간단한 데이터 처리
    data = {
        "message": "Hello, <PERSON>ocorp!",
        "status": "success",
        "timestamp": "2024-01-01"
    }

    log.info(f"처리된 데이터: {data}")

    # 파일에 결과 저장
    with open("output.json", "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    log.info("✅ 작업 완료! output.json 파일이 생성되었습니다.")


@task
def web_data_fetch():
    """
    웹에서 데이터를 가져오는 예제
    """
    log.info("🌐 웹 데이터 가져오기 시작...")

    try:
        # JSONPlaceholder API에서 샘플 데이터 가져오기
        response = requests.get("https://jsonplaceholder.typicode.com/posts/1")
        response.raise_for_status()

        data = response.json()
        log.info(f"가져온 데이터: {data['title']}")

        # 결과 저장
        with open("web_data.json", "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        log.info("✅ 웹 데이터 가져오기 완료!")

    except Exception as e:
        log.error(f"❌ 오류 발생: {e}")


@task
def process_multiple_data():
    """
    여러 데이터를 처리하는 예제
    """
    log.info("📊 다중 데이터 처리 시작...")

    # 샘플 데이터 리스트
    items = [
        {"name": "항목1", "value": 100},
        {"name": "항목2", "value": 200},
        {"name": "항목3", "value": 300},
    ]

    processed_items = []

    for item in items:
        # 각 항목에 대해 처리 수행
        processed_item = {
            "original_name": item["name"],
            "processed_name": f"처리된_{item['name']}",
            "doubled_value": item["value"] * 2,
            "status": "processed"
        }
        processed_items.append(processed_item)
        log.info(f"처리 완료: {processed_item['processed_name']}")

    # 결과 저장
    result = {
        "total_items": len(processed_items),
        "items": processed_items,
        "summary": "모든 항목이 성공적으로 처리되었습니다."
    }

    with open("processed_data.json", "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    log.info(f"✅ {len(processed_items)}개 항목 처리 완료!")


@task
def browser_automation():
    """
    브라우저 자동화 예제 - 웹사이트 방문 및 정보 수집
    """
    log.info("🌐 브라우저 자동화 시작...")

    try:
        # 브라우저 설정
        browser.configure(
            browser_engine="chromium",
            headless=False,  # 브라우저 창을 보이게 설정
            slowmo=1000,     # 동작을 천천히 (1초 지연)
        )

        # 새 페이지 열기
        page = browser.goto("https://example.com")

        # 페이지 제목 가져오기
        title = page.title()
        log.info(f"페이지 제목: {title}")

        # 페이지 내용 가져오기
        content = page.locator("h1").text_content()
        log.info(f"메인 제목: {content}")

        # 스크린샷 찍기
        page.screenshot(path="example_screenshot.png")
        log.info("스크린샷 저장 완료: example_screenshot.png")

        # 결과 저장
        result = {
            "url": "https://example.com",
            "title": title,
            "main_heading": content,
            "screenshot": "example_screenshot.png",
            "status": "success"
        }

        with open("browser_result.json", "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        log.info("✅ 브라우저 자동화 완료!")

    except Exception as e:
        log.error(f"❌ 브라우저 자동화 오류: {e}")

    finally:
        # 브라우저 닫기
        try:
            browser.page().close()
        except:
            pass


if __name__ == "__main__":
    # 로컬에서 테스트할 때 사용
    hello_robocorp()
    web_data_fetch()
    process_multiple_data()
    browser_automation()
