# 🤖 Robocorp 자동화 테스트 프로젝트

이 프로젝트는 Robocorp 플랫폼을 사용한 Python 기반 자동화 예제들을 포함합니다.

## 📋 프로젝트 구조

```
rpa-test/
├── tasks.py              # 메인 자동화 작업들
├── robot.yaml           # Robocorp 설정 파일
├── conda.yaml           # Python 환경 설정
├── README.md            # 프로젝트 설명
├── output/              # 실행 로그 및 결과
│   ├── log.html        # HTML 로그 파일
│   └── output.robolog  # Robocorp 로그 파일
├── output.json          # 기본 작업 결과
├── web_data.json        # 웹 데이터 가져오기 결과
├── processed_data.json  # 다중 데이터 처리 결과
├── browser_result.json  # 브라우저 자동화 결과
└── example_screenshot.png # 웹사이트 스크린샷
```

## 🚀 포함된 자동화 작업들

### 1. Hello Robocorp (`hello_robocorp`)
- 기본적인 Robocorp 작업 예제
- 간단한 데이터 처리 및 JSON 파일 생성

### 2. 웹 데이터 가져오기 (`web_data_fetch`)
- REST API를 통한 데이터 수집
- JSONPlaceholder API 사용 예제

### 3. 다중 데이터 처리 (`process_multiple_data`)
- 여러 데이터 항목의 일괄 처리
- 데이터 변환 및 결과 저장

### 4. 브라우저 자동화 (`browser_automation`)
- Playwright를 사용한 웹 브라우저 제어
- 웹사이트 방문, 정보 수집, 스크린샷 촬영

## 🛠️ 설치 및 실행

### 1. 필요 조건
- Python 3.9+
- pip

### 2. 설치
```bash
# Robocorp 메타패키지 설치
pip install robocorp

# 브라우저 자동화를 위한 추가 패키지
pip install robocorp-browser

# Playwright 브라우저 설치
playwright install
```

### 3. 개별 작업 실행
```bash
# 기본 작업
python -m robocorp.tasks run tasks.py -t hello_robocorp

# 웹 데이터 가져오기
python -m robocorp.tasks run tasks.py -t web_data_fetch

# 다중 데이터 처리
python -m robocorp.tasks run tasks.py -t process_multiple_data

# 브라우저 자동화
python -m robocorp.tasks run tasks.py -t browser_automation
```

### 4. 모든 작업 실행
```bash
# Python 스크립트로 직접 실행
python tasks.py
```

## 📊 결과 확인

### 로그 파일
- `output/log.html`: 브라우저에서 열어볼 수 있는 상세한 실행 로그

### 결과 파일
- `output.json`: 기본 작업 결과
- `web_data.json`: 웹에서 가져온 데이터
- `processed_data.json`: 처리된 데이터 목록
- `browser_result.json`: 브라우저 자동화 결과
- `example_screenshot.png`: 웹사이트 스크린샷

## 🔧 주요 기능

### Robocorp 라이브러리 사용
- `robocorp.tasks`: 작업 정의 및 실행
- `robocorp.log`: 로깅 및 디버깅
- `robocorp.browser`: 웹 브라우저 자동화

### 자동화 기능
- ✅ REST API 호출 및 데이터 처리
- ✅ JSON 파일 읽기/쓰기
- ✅ 웹 브라우저 제어
- ✅ 스크린샷 촬영
- ✅ 에러 처리 및 로깅

## 📚 추가 학습 자료

- [Robocorp 공식 문서](https://robocorp.com/docs/)
- [Robocorp 포털](https://robocorp.com/portal)
- [Python 자동화 코스](https://robocorp.com/docs/courses)

## 🤝 기여하기

이 프로젝트는 Robocorp 학습 목적으로 만들어졌습니다. 
개선사항이나 새로운 예제가 있다면 언제든 추가해보세요!

---

**Happy Automating! 🤖✨**
