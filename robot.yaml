tasks:
  # 첫 번째 Robocorp 자동화 작업들
  Hello Robocorp:
    shell: python -m robocorp.tasks run tasks.py -t hello_robocorp
  
  Web Data Fetch:
    shell: python -m robocorp.tasks run tasks.py -t web_data_fetch
  
  Process Multiple Data:
    shell: python -m robocorp.tasks run tasks.py -t process_multiple_data

condaConfigFile: conda.yaml

artifactsDir: output

PATH:
  - .
PYTHONPATH:
  - .

ignoreFiles:
  - .gitignore
  - "*.pyc"
  - __pycache__/
  - "*.log"
